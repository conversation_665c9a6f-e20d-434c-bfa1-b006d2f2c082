import React, { useEffect } from 'react';

import ConsultingRoomsHero from '@/components/consulting-rooms/ConsultingRoomsHero';
import ContactCTA from '@/components/consulting-rooms/ContactCTA';
import FacilitiesSection from '@/components/consulting-rooms/FacilitiesSection';
import FeaturesSection from '@/components/consulting-rooms/FeaturesSection';
import GallerySection from '@/components/consulting-rooms/GallerySection';
import InquiryForm from '@/components/consulting-rooms/InquiryForm';
import StandardPageLayout from '@/components/StandardPageLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { getConsultingRoomsData } from '@/data/consultingRooms/consultingRoomsData';
import { generatePageSEO } from '@/lib/seo';
import en from '@/locales/en';

/**
 * Consulting Rooms Component
 * Unified implementation combining comprehensive content with modular architecture
 * Includes all sections from both original and refactored versions
 */

const ConsultingRooms: React.FC = () => {
  const { t } = useLanguage();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Get consulting rooms configuration with translations
  const safeT = t || en;
  const config = getConsultingRoomsData(safeT);

  // Generate SEO data for consulting rooms page
  const consultingRoomsSeoData = generatePageSEO('service', {
    title: 'Consulting Rooms | miNEURO',
    description: 'Professional consulting rooms available for medical practitioners. State-of-the-art facilities with modern equipment and patient-focused amenities.',
    keywords: ['consulting rooms', 'medical facilities', 'professional practice space', 'neurosurgery consulting']
  });

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real application, you would send the form data to a server
    alert(config.form.thankYouMessage);
  };

  return (
    <StandardPageLayout
      title="Consulting Rooms"
      subtitle="Professional consulting rooms with state-of-the-art facilities"
      pageType="service"
      seoData={consultingRoomsSeoData}
      showHeader={false}
    >
      <main className="flex-1">
        {/* Hero Section */}
        <ConsultingRoomsHero
          subtitle={config.hero.subtitle}
          title={config.hero.title}
          description={config.hero.description}
          ctaText={config.hero.ctaText}
          ctaLink={config.hero.ctaLink}
        />

        {/* Features Section */}
        <FeaturesSection
          title={config.whyChoose.title}
          description={config.whyChoose.description}
          features={config.features}
        />

        {/* Main Content Grid */}
        <section className="pb-16">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Facilities */}
              <FacilitiesSection
                title={config.facilities.title}
                facilities={config.facilities.items}
                locationTitle={config.location.title}
                locationDescription={config.location.description}
                locationAddress={config.location.address}
                mainImage={config.facilities.mainImage}
              />

              {/* Inquiry Form */}
              <InquiryForm
                title={config.form.title}
                fields={config.form.fields}
                submitText={config.form.submitText}
                confirmationText={config.form.confirmationText}
                thankYouMessage={config.form.thankYouMessage}
                onSubmit={handleFormSubmit}
              />
            </div>
          </div>
        </section>

        {/* Accessibility Features Section - Enhanced value proposition */}
        <section className="py-16 bg-muted/30">
          <div className="container max-w-7xl">
            <div className="text-center mb-12">
              <h2 className="text-enhanced-heading text-3xl font-bold mb-4">Accessibility & Patient Comfort</h2>
              <p className="text-muted-foreground max-w-3xl mx-auto">
                Our consulting rooms are designed with accessibility and patient comfort as top priorities,
                ensuring all patients can access our facilities with ease and dignity.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="card p-6 rounded-lg shadow-md medical-card text-center">
                <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold mb-2">Wheelchair Access</h3>
                <p className="text-muted-foreground text-sm">
                  Full wheelchair accessibility with ramps, wide doorways, and accessible parking spaces.
                </p>
              </div>

              <div className="card p-6 rounded-lg shadow-md medical-card text-center">
                <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold mb-2">Mobility Assistance</h3>
                <p className="text-muted-foreground text-sm">
                  Support for patients with mobility challenges, including assistance devices and comfortable seating.
                </p>
              </div>

              <div className="card p-6 rounded-lg shadow-md medical-card text-center">
                <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold mb-2">Visual & Hearing Support</h3>
                <p className="text-muted-foreground text-sm">
                  Clear signage, good lighting, and hearing loop systems to support patients with sensory needs.
                </p>
              </div>

              <div className="card p-6 rounded-lg shadow-md medical-card text-center">
                <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold mb-2">Special Needs Support</h3>
                <p className="text-muted-foreground text-sm">
                  Dedicated support for patients with special needs, including quiet spaces and flexible scheduling.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Gallery Section */}
        <GallerySection
          title={config.gallery.title}
          description={config.gallery.description}
          images={config.gallery.images}
        />

        {/* Contact CTA */}
        <ContactCTA
          title={config.cta.title}
          description={config.cta.description}
          buttonText={config.cta.buttonText}
          buttonLink={config.cta.buttonLink}
        />
      </main>
    </StandardPageLayout>
  );
};

ConsultingRooms.displayName = 'ConsultingRooms';

export default ConsultingRooms;
