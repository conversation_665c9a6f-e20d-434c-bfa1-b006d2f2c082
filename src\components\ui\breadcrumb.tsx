/**
 * Breadcrumb components have been removed for cleaner navigation design.
 * This file is kept for backward compatibility but exports empty components.
 */

// Empty placeholder components for backward compatibility
const Breadcrumb = () => null;
const BreadcrumbList = () => null;
const BreadcrumbItem = () => null;
const BreadcrumbLink = () => null;
const BreadcrumbPage = () => null;
const BreadcrumbSeparator = () => null;
const BreadcrumbEllipsis = () => null;

// Set display names for debugging
Breadcrumb.displayName = "Breadcrumb";
BreadcrumbList.displayName = "BreadcrumbList";
BreadcrumbItem.displayName = "BreadcrumbItem";
BreadcrumbLink.displayName = "BreadcrumbLink";
BreadcrumbPage.displayName = "BreadcrumbPage";
BreadcrumbSeparator.displayName = "BreadcrumbSeparator";
BreadcrumbEllipsis.displayName = "BreadcrumbEllipsis";

export {
  Breadcrumb,
  <PERSON>read<PERSON>rumbList,
  <PERSON>readcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
  BreadcrumbEllipsis,
};
