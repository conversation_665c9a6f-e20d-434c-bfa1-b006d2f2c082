import { Target, Calendar } from 'lucide-react';
import React from 'react';
import { Link } from 'react-router-dom';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';

/**
 * Condition Hero Component
 * Reusable hero section for medical condition pages
 */

// Updated props interface - breadcrumbs removed for clean design
interface ConditionHeroProps {
  data: {
    title: string;
    subtitle?: string;
    backgroundImage?: string;
  };
}

const ConditionHero: React.FC<ConditionHeroProps> = React.memo(({
  data
}) => {
  // Always call hooks at the top level - use error boundaries for error handling
  const deviceInfo = useDeviceDetection();
  const { t } = useLanguage();

  const { title, subtitle, backgroundImage } = data;

  return (
    <div>

      {/* Hero Section */}
      <header
        className={cn(
          "relative bg-gradient-to-r from-primary/10 to-white dark:from-primary/20 dark:to-background",
          deviceInfo.isMobile ? "py-12 mobile-optimized" : "py-20",
          deviceInfo.isTablet ? "tablet-optimized" : "",
          deviceInfo.isDesktop ? "desktop-optimized" : ""
        )}
        data-testid="condition-hero"
        aria-label={`${title} hero section`}
        style={backgroundImage ? { backgroundImage: `url(${backgroundImage})` } : undefined}
      >
        {backgroundImage && (
          <div className="absolute inset-0 overflow-hidden opacity-10">
            <SafeImage
              src={backgroundImage}
              alt={`${title} background`}
              className="w-full h-full object-cover"
              fallbackSrc="https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
            />
          </div>
        )}
        <div className="container-spacing">
          <div className="text-center content-width-standard">
            <Badge variant="secondary" className="mb-6 text-sm md:text-base px-4 py-2">
              {t.common?.spineConditionsLibrary || 'Spine Conditions Library'}
            </Badge>
            <h1 className="medical-heading mb-8">
              {title}
            </h1>
            {subtitle && (
              <p className="medical-lead mb-12">
                {subtitle}
              </p>
            )}
            <div className="flex flex-col sm:flex-row justify-center gap-6">
              <Button asChild size="lg" >
                <Link to="/#assessment" >
                  <Target className="mr-2 h-5 w-5" />
                  {t.common?.takeAssessment || 'Take Assessment'}
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" >
                <Link to="/contact" className="text-primary hover:">
                  <Calendar className="mr-2 h-5 w-5" />
                  {t.common?.bookConsultation || 'Book Consultation'}
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </header>
      {/* Quick Facts Section - Placeholder for future implementation */}
      <div className={cn(
        "bg-muted/30",
        deviceInfo.isMobile ? "py-8" : "py-12"
      )}>
        <div className={cn("container", deviceInfo.isMobile ? "px-4" : "")}>
          <div className={cn("grid", deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2 md:grid-cols-4")}>
            {/* Quick facts can be added here in the future */}
          </div>
        </div>
      </div>
    </div>
  );
});

ConditionHero.displayName = 'ConditionHero';

export default ConditionHero;
