import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import BenefitsGrid from '@/components/expertise/BenefitsGrid';
import ExpertiseCallToAction from '@/components/expertise/ExpertiseCallToAction';
import ExpertiseHero from '@/components/expertise/ExpertiseHero';
import ExpertiseSection from '@/components/expertise/ExpertiseSection';
import ExpertiseSidebar from '@/components/expertise/ExpertiseSidebar';
import SafeImage from '@/components/SafeImage';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Button } from '@/components/ui/button';
import { cervicalDiscReplacementData } from '@/data/expertise/cervicalDiscReplacementData';

/**
 * Cervical Disc Replacement Component
 * Unified implementation combining comprehensive content with modular architecture
 * Includes all sections from both original and refactored versions plus documentation content
 */

const CervicalDiscReplacement: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const data = cervicalDiscReplacementData;

  // Sub-pages navigation data
  const subPages = [
    {
      title: 'Technology & Implants',
      description: 'Explore the advanced technology behind artificial cervical discs, materials, and device examples.',
      link: '/expertise/cervical-disc-replacement/technology-implants',
      highlights: ['Implant materials & designs', 'Biomechanical engineering', 'Device examples (Bryan, Mobi-C, M6)', 'Historical development']
    },
    {
      title: 'Surgery & Recovery',
      description: 'Comprehensive guide to the surgical procedure, patient selection, and recovery process.',
      link: '/expertise/cervical-disc-replacement/surgery-recovery',
      highlights: ['Patient selection criteria', 'Surgical technique details', 'Recovery timeline', 'Postoperative care']
    },
    {
      title: 'Risks & Comparison',
      description: 'Understanding risks, complications, and how disc replacement compares to fusion surgery.',
      link: '/expertise/cervical-disc-replacement/risks-comparison',
      highlights: ['Risk assessment', 'Safety statistics', 'Comparison with ACDF', 'Clinical outcomes']
    }
  ];



  return (
    <StandardPageLayout title="Cervical Disc Replacement" showHeader={false}>
      <ExpertiseHero
        title={data.hero.title}
        subtitle={data.hero.subtitle}
        backgroundImage={data.hero.backgroundImage}
      />

      <div className="flex-1">


        {/* Main Content */}
        <section className="section-spacing">
          <div className="section-container">
            <div className="medical-content-grid lg:grid-cols-3 gap-12">
              <div className="lg:col-span-2">
                {/* Original Sections */}
                {data.sections.map((section) => (
                  <ExpertiseSection key={section.id} section={section} />
                ))}

                {/* Sub-pages Navigation */}
                <div className="medical-section-divider">
                  <h2 className="medical-heading">Explore Detailed Information</h2>
                  <p className="medical-body mb-8">
                    Dive deeper into specific aspects of cervical disc replacement with our comprehensive sub-pages:
                  </p>

                  <div className="grid grid-cols-1 gap-6">
                    {subPages.map((subPage, index) => (
                      <div key={index} className="medical-content-card medical-card-interactive">
                        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                          <div className="flex-1 mb-6 lg:mb-0">
                            <h3 className="medical-subheading text-primary mb-4">{subPage.title}</h3>
                            <p className="medical-body mb-6">{subPage.description}</p>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              {subPage.highlights.map((highlight, highlightIndex) => (
                                <div key={highlightIndex} className="flex items-center text-sm text-muted-foreground">
                                  <span className="w-2 h-2 bg-primary rounded-full mr-3 flex-shrink-0"></span>
                                  {highlight}
                                </div>
                              ))}
                            </div>
                          </div>
                          <div className="lg:ml-8">
                            <Button asChild size="lg" >
                              <Link to={subPage.link}>
                                Learn More →
                              </Link>
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Recommendations */}
                <div className="medical-section-divider">
                  <h2 className="medical-heading">{data.recommendations.title}</h2>
                  <p className="medical-body mb-8">
                    {data.recommendations.description}
                  </p>
                  <div className="medical-info-panel">
                    <ul className="space-y-4">
                      {data.recommendations.conditions.map((condition, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                          <span className="medical-body mb-0">{condition}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                {/* Medical Image Showcase */}
                <div className="medical-section-divider">
                  <h2 className="medical-heading">Cervical Disc Implant Technology</h2>
                  <p className="medical-body mb-8">
                    Modern cervical disc implants are engineered to replicate the natural movement and function of healthy cervical discs, providing patients with motion preservation and pain relief.
                  </p>

                  <div className="medical-image-container medical-content-card">
                    <SafeImage
                      src={data.images.implants.src}
                      alt={data.images.implants.alt}
                      className="w-full h-auto object-cover rounded-lg"
                      fallbackSrc="/images/medical-consulting.jpg"
                    />
                    <div className="medical-caption text-center mt-6">
                      <p className="font-medium text-foreground mb-2">Advanced Cervical Disc Implants</p>
                      <p className="text-sm text-muted-foreground">
                        Various models of artificial cervical discs designed to preserve natural neck motion while providing structural support and pain relief.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Benefits Grid */}
                <BenefitsGrid
                  title="Key Benefits of Cervical Disc Replacement"
                  benefits={data.benefits}
                />

                {/* Quick Overview of Key Benefits */}
                <div className="medical-section-divider">
                  <h2 className="medical-heading">Why Choose Cervical Disc Replacement?</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="medical-content-card text-center">
                      <div className="text-4xl font-bold text-primary mb-3">80-90%</div>
                      <div className="medical-body mb-0 font-medium">Patient satisfaction rates</div>
                    </div>
                    <div className="medical-content-card text-center">
                      <div className="text-4xl font-bold text-primary mb-3">50%</div>
                      <div className="medical-body mb-0 font-medium">Reduction in adjacent segment disease</div>
                    </div>
                    <div className="medical-content-card text-center">
                      <div className="text-4xl font-bold text-primary mb-3">&lt;1%</div>
                      <div className="medical-body mb-0 font-medium">Risk of permanent disability</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <ExpertiseSidebar sections={data.sidebar} />
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <ExpertiseCallToAction
          title={data.callToAction.title}
          description={data.callToAction.description}
          primaryButton={data.callToAction.primaryButton}
          secondaryButton={data.callToAction.secondaryButton}
        />

        {/* Medical Disclaimer */}
        <section className="section-background border-t border-border/30">
          <div className="section-container py-12">
            <div className="medical-info-panel content-width-standard">
              <h3 className="medical-subheading text-center mb-6">Important Medical Information</h3>
              <div className="space-y-4 text-sm text-muted-foreground">
                <p>
                  This information is provided for educational purposes only and should not be considered as medical advice.
                  Individual treatment outcomes may vary based on patient-specific factors, medical history, and condition severity.
                </p>
                <p>
                  Dr Ales Aliashkevich is a qualified neurosurgeon with extensive experience in cervical disc replacement procedures.
                  All treatment decisions should be made in consultation with qualified medical professionals after thorough evaluation.
                </p>
                <p className="text-center font-medium text-foreground">
                  For personalised medical advice, please schedule a consultation with Dr Aliashkevich.
                </p>
              </div>
            </div>
          </div>
        </section>
      </div>
    </StandardPageLayout>
  );
};

CervicalDiscReplacement.displayName = 'CervicalDiscReplacement';

export default CervicalDiscReplacement;
